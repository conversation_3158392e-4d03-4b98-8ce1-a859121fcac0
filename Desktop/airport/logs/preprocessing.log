2025-08-11 14:27:34,925 - __main__ - INFO - Starting run_preprocessing_pipeline...
2025-08-11 14:27:34,925 - __main__ - INFO - Starting preprocessing pipeline for airport_traffic...
2025-08-11 14:27:34,925 - __main__ - INFO - Loading airport_traffic from auto source...
2025-08-11 14:27:36,742 - __main__ - INFO - Loaded airport_traffic from SQLite: 1000000 rows
2025-08-11 14:27:36,743 - __main__ - INFO - Starting clean_missing_values...
2025-08-11 14:27:36,743 - __main__ - INFO - Cleaning missing values for airport_traffic...
2025-08-11 14:27:37,414 - __main__ - INFO - No missing values found in airport_traffic
2025-08-11 14:27:37,414 - __main__ - INFO - Completed clean_missing_values in 0.67 seconds
2025-08-11 14:27:37,488 - __main__ - INFO - Starting remove_duplicates...
2025-08-11 14:27:37,488 - __main__ - INFO - Removing duplicates from airport_traffic...
2025-08-11 14:27:38,389 - __main__ - INFO - No duplicates found in airport_traffic
2025-08-11 14:27:38,389 - __main__ - INFO - Completed remove_duplicates in 0.90 seconds
2025-08-11 14:27:38,389 - __main__ - INFO - Starting standardize_data_types...
2025-08-11 14:27:38,389 - __main__ - INFO - Standardizing data types for airport_traffic...
2025-08-11 14:27:39,036 - __main__ - INFO - Data type standardization completed for airport_traffic
2025-08-11 14:27:39,036 - __main__ - INFO - Completed standardize_data_types in 0.65 seconds
2025-08-11 14:27:39,115 - __main__ - INFO - Starting detect_and_handle_outliers...
2025-08-11 14:27:39,115 - __main__ - INFO - Detecting and handling outliers in airport_traffic using iqr method...
2025-08-11 14:27:39,359 - __main__ - INFO - Handled 32094 outliers in passenger_count (3.21%)
2025-08-11 14:27:39,359 - __main__ - INFO - Outlier handling completed for airport_traffic
2025-08-11 14:27:39,359 - __main__ - INFO - Completed detect_and_handle_outliers in 0.24 seconds
2025-08-11 14:27:39,492 - __main__ - INFO - Starting create_derived_features...
2025-08-11 14:27:39,492 - __main__ - INFO - Creating derived features for airport_traffic...
2025-08-11 14:27:39,715 - __main__ - INFO - Created 6 derived features for airport_traffic
2025-08-11 14:27:39,716 - __main__ - INFO - Completed create_derived_features in 0.22 seconds
2025-08-11 14:27:39,747 - __main__ - INFO - Preprocessing completed for airport_traffic: (1000000, 9) -> (1000000, 15)
2025-08-11 14:27:39,748 - __main__ - INFO - Completed run_preprocessing_pipeline in 4.82 seconds
2025-08-11 14:27:40,843 - __main__ - INFO - Saved preprocessed airport_traffic to processed_data/airport_traffic_preprocessed_20250811_142739.pkl
2025-08-11 14:27:44,960 - __main__ - INFO - Saved preprocessed airport_traffic to processed_data/airport_traffic_preprocessed_20250811_142740.csv
2025-08-11 14:27:44,961 - __main__ - INFO - Starting run_preprocessing_pipeline...
2025-08-11 14:27:44,961 - __main__ - INFO - Starting preprocessing pipeline for flight_info...
2025-08-11 14:27:44,961 - __main__ - INFO - Loading flight_info from auto source...
2025-08-11 14:27:48,241 - __main__ - INFO - Loaded flight_info from SQLite: 1000000 rows
2025-08-11 14:27:48,242 - __main__ - INFO - Starting clean_missing_values...
2025-08-11 14:27:48,242 - __main__ - INFO - Cleaning missing values for flight_info...
2025-08-11 14:27:49,060 - __main__ - INFO - No missing values found in flight_info
2025-08-11 14:27:49,064 - __main__ - INFO - Completed clean_missing_values in 0.82 seconds
2025-08-11 14:27:49,339 - __main__ - INFO - Starting remove_duplicates...
2025-08-11 14:27:49,339 - __main__ - INFO - Removing duplicates from flight_info...
2025-08-11 14:27:50,315 - __main__ - INFO - No duplicates found in flight_info
2025-08-11 14:27:50,315 - __main__ - INFO - Completed remove_duplicates in 0.98 seconds
2025-08-11 14:27:50,315 - __main__ - INFO - Starting standardize_data_types...
2025-08-11 14:27:50,315 - __main__ - INFO - Standardizing data types for flight_info...
2025-08-11 14:27:51,228 - __main__ - INFO - Data type standardization completed for flight_info
2025-08-11 14:27:51,229 - __main__ - INFO - Completed standardize_data_types in 0.91 seconds
2025-08-11 14:27:51,312 - __main__ - INFO - Starting detect_and_handle_outliers...
2025-08-11 14:27:51,312 - __main__ - INFO - Detecting and handling outliers in flight_info using iqr method...
2025-08-11 14:27:51,541 - __main__ - INFO - Handled 131015 outliers in passenger_count (13.10%)
2025-08-11 14:27:51,569 - __main__ - INFO - Handled 27193 outliers in delay_minutes (2.72%)
2025-08-11 14:27:51,570 - __main__ - INFO - Outlier handling completed for flight_info
2025-08-11 14:27:51,570 - __main__ - INFO - Completed detect_and_handle_outliers in 0.26 seconds
2025-08-11 14:27:51,740 - __main__ - INFO - Starting create_derived_features...
2025-08-11 14:27:51,740 - __main__ - INFO - Creating derived features for flight_info...
2025-08-11 14:27:51,924 - __main__ - INFO - Created 6 derived features for flight_info
2025-08-11 14:27:51,925 - __main__ - INFO - Completed create_derived_features in 0.18 seconds
2025-08-11 14:27:52,033 - __main__ - INFO - Preprocessing completed for flight_info: (1000000, 10) -> (1000000, 16)
2025-08-11 14:27:52,033 - __main__ - INFO - Completed run_preprocessing_pipeline in 7.07 seconds
2025-08-11 14:27:53,373 - __main__ - INFO - Saved preprocessed flight_info to processed_data/flight_info_preprocessed_20250811_142752.pkl
2025-08-11 14:27:59,124 - __main__ - INFO - Saved preprocessed flight_info to processed_data/flight_info_preprocessed_20250811_142753.csv
2025-08-11 14:27:59,124 - __main__ - INFO - Starting run_preprocessing_pipeline...
2025-08-11 14:27:59,124 - __main__ - INFO - Starting preprocessing pipeline for passenger_profiles...
2025-08-11 14:27:59,124 - __main__ - INFO - Loading passenger_profiles from auto source...
2025-08-11 14:28:05,163 - __main__ - INFO - Loaded passenger_profiles from SQLite: 1000000 rows
2025-08-11 14:28:05,167 - __main__ - INFO - Starting clean_missing_values...
2025-08-11 14:28:05,167 - __main__ - INFO - Cleaning missing values for passenger_profiles...
2025-08-11 14:28:06,533 - __main__ - INFO - No missing values found in passenger_profiles
2025-08-11 14:28:06,533 - __main__ - INFO - Completed clean_missing_values in 1.37 seconds
2025-08-11 14:28:06,629 - __main__ - INFO - Starting remove_duplicates...
2025-08-11 14:28:06,629 - __main__ - INFO - Removing duplicates from passenger_profiles...
2025-08-11 14:28:07,821 - __main__ - INFO - No duplicates found in passenger_profiles
2025-08-11 14:28:07,822 - __main__ - INFO - Completed remove_duplicates in 1.19 seconds
2025-08-11 14:28:07,822 - __main__ - INFO - Starting standardize_data_types...
2025-08-11 14:28:07,822 - __main__ - INFO - Standardizing data types for passenger_profiles...
2025-08-11 14:28:09,317 - __main__ - INFO - Data type standardization completed for passenger_profiles
2025-08-11 14:28:09,317 - __main__ - INFO - Completed standardize_data_types in 1.50 seconds
2025-08-11 14:28:09,515 - __main__ - INFO - Starting detect_and_handle_outliers...
2025-08-11 14:28:09,515 - __main__ - INFO - Detecting and handling outliers in passenger_profiles using iqr method...
2025-08-11 14:28:09,808 - __main__ - INFO - Handled 124313 outliers in average_spend (12.43%)
2025-08-11 14:28:09,836 - __main__ - INFO - Handled 68803 outliers in num_categories_visited (6.88%)
2025-08-11 14:28:09,836 - __main__ - INFO - Outlier handling completed for passenger_profiles
2025-08-11 14:28:09,836 - __main__ - INFO - Completed detect_and_handle_outliers in 0.32 seconds
2025-08-11 14:28:10,328 - __main__ - INFO - Starting create_derived_features...
2025-08-11 14:28:10,328 - __main__ - INFO - Creating derived features for passenger_profiles...
2025-08-11 14:28:10,529 - __main__ - INFO - Created 4 derived features for passenger_profiles
2025-08-11 14:28:10,529 - __main__ - INFO - Completed create_derived_features in 0.20 seconds
2025-08-11 14:28:10,613 - __main__ - INFO - Preprocessing completed for passenger_profiles: (1000000, 14) -> (1000000, 18)
2025-08-11 14:28:10,613 - __main__ - INFO - Completed run_preprocessing_pipeline in 11.49 seconds
2025-08-11 14:28:12,111 - __main__ - INFO - Saved preprocessed passenger_profiles to processed_data/passenger_profiles_preprocessed_20250811_142810.pkl
2025-08-11 14:28:16,336 - __main__ - INFO - Saved preprocessed passenger_profiles to processed_data/passenger_profiles_preprocessed_20250811_142812.csv
2025-08-11 14:28:16,337 - __main__ - INFO - Starting run_preprocessing_pipeline...
2025-08-11 14:28:16,337 - __main__ - INFO - Starting preprocessing pipeline for esop_transactions...
2025-08-11 14:28:16,337 - __main__ - INFO - Loading esop_transactions from auto source...
2025-08-11 14:28:16,338 - __main__ - ERROR - Failed run_preprocessing_pipeline after 0.00 seconds: No pickle files found for esop_transactions
2025-08-11 14:28:16,338 - __main__ - ERROR - Failed to process esop_transactions: No pickle files found for esop_transactions
