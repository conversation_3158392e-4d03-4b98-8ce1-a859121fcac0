2025-08-11 14:26:56,299 - __main__ - INFO - Starting run_ingestion_pipeline...
2025-08-11 14:26:56,300 - __main__ - INFO - Starting data ingestion pipeline...
2025-08-11 14:26:56,300 - __main__ - INFO - Starting load_csv_to_df...
2025-08-11 14:26:56,300 - __main__ - INFO - Loading airport_traffic from data/airport_traffic.csv...
2025-08-11 14:26:57,343 - __main__ - INFO - Successfully loaded airport_traffic: 1000000 rows, 9 columns
2025-08-11 14:26:57,345 - __main__ - INFO - Columns: ['timestamp', 'airport', 'terminal_id', 'zone_id', 'passenger_count', 'flight_id', 'passenger_type', 'flight_type', 'destination']
2025-08-11 14:26:58,030 - __main__ - INFO - Memory usage for airport_traffic: 502.02 MB
2025-08-11 14:26:58,030 - __main__ - INFO - Completed load_csv_to_df in 1.73 seconds
2025-08-11 14:26:58,030 - __main__ - INFO - Starting validate_data_quality...
2025-08-11 14:26:58,030 - __main__ - INFO - Validating data quality for airport_traffic...
2025-08-11 14:26:59,083 - __main__ - INFO - Data quality validation completed for airport_traffic
2025-08-11 14:26:59,083 - __main__ - INFO - Completed validate_data_quality in 1.05 seconds
2025-08-11 14:26:59,750 - __main__ - INFO - Starting load_csv_to_df...
2025-08-11 14:26:59,750 - __main__ - INFO - Loading esop_transactions from data/esop_transactions.csv...
2025-08-11 14:26:59,768 - __main__ - INFO - Successfully loaded esop_transactions: 10000 rows, 6 columns
2025-08-11 14:26:59,768 - __main__ - INFO - Columns: ['store_name', 'store_category', 'location', 'date', 'product_id', 'net_amount']
2025-08-11 14:26:59,774 - __main__ - INFO - Memory usage for esop_transactions: 3.36 MB
2025-08-11 14:26:59,774 - __main__ - INFO - Completed load_csv_to_df in 0.02 seconds
2025-08-11 14:26:59,774 - __main__ - INFO - Starting validate_data_quality...
2025-08-11 14:26:59,774 - __main__ - INFO - Validating data quality for esop_transactions...
2025-08-11 14:26:59,784 - __main__ - INFO - Data quality validation completed for esop_transactions
2025-08-11 14:26:59,784 - __main__ - INFO - Completed validate_data_quality in 0.01 seconds
2025-08-11 14:26:59,789 - __main__ - INFO - Starting load_csv_to_df...
2025-08-11 14:26:59,789 - __main__ - INFO - Loading flight_info from data/flight_info.csv.csv...
2025-08-11 14:27:00,729 - __main__ - INFO - Successfully loaded flight_info: 1000000 rows, 10 columns
2025-08-11 14:27:00,729 - __main__ - INFO - Columns: ['flight_id', 'airline', 'origin', 'destination', 'scheduled_time', 'actual_time', 'flight_type', 'passenger_count', 'aircraft_type', 'delay_minutes']
2025-08-11 14:27:01,430 - __main__ - INFO - Memory usage for flight_info: 516.84 MB
2025-08-11 14:27:01,430 - __main__ - INFO - Completed load_csv_to_df in 1.64 seconds
2025-08-11 14:27:01,430 - __main__ - INFO - Starting validate_data_quality...
2025-08-11 14:27:01,430 - __main__ - INFO - Validating data quality for flight_info...
2025-08-11 14:27:02,304 - __main__ - INFO - Data quality validation completed for flight_info
2025-08-11 14:27:02,304 - __main__ - INFO - Completed validate_data_quality in 0.87 seconds
2025-08-11 14:27:02,989 - __main__ - INFO - Starting load_csv_to_df...
2025-08-11 14:27:02,989 - __main__ - INFO - Loading passenger_profiles from data/passenger_profiles.csv...
2025-08-11 14:27:04,380 - __main__ - INFO - Successfully loaded passenger_profiles: 1000000 rows, 14 columns
2025-08-11 14:27:04,381 - __main__ - INFO - Columns: ['passenger_id', 'age', 'gender', 'nationality', 'marital_status', 'passenger_segment', 'class_of_travel', 'income_bracket', 'loyalty_program_member', 'dwell_time', 'purchase_categories', 'average_spend', 'made_purchase', 'num_categories_visited']
2025-08-11 14:27:05,071 - __main__ - INFO - Memory usage for passenger_profiles: 547.88 MB
2025-08-11 14:27:05,071 - __main__ - INFO - Completed load_csv_to_df in 2.08 seconds
2025-08-11 14:27:05,071 - __main__ - INFO - Starting validate_data_quality...
2025-08-11 14:27:05,071 - __main__ - INFO - Validating data quality for passenger_profiles...
2025-08-11 14:27:05,788 - __main__ - INFO - Data quality validation completed for passenger_profiles
2025-08-11 14:27:05,788 - __main__ - INFO - Completed validate_data_quality in 0.72 seconds
2025-08-11 14:27:06,456 - __main__ - INFO - Starting create_sqlite_db...
2025-08-11 14:27:06,456 - __main__ - INFO - Creating SQLite database at airport_data.db...
2025-08-11 14:27:06,458 - __main__ - INFO - Storing airport_traffic in SQLite (large dataset: 1000000 rows)
2025-08-11 14:27:07,757 - __main__ - INFO - Successfully stored airport_traffic with 1000000 rows
2025-08-11 14:27:07,757 - __main__ - INFO - Storing flight_info in SQLite (large dataset: 1000000 rows)
2025-08-11 14:27:09,289 - __main__ - INFO - Successfully stored flight_info with 1000000 rows
2025-08-11 14:27:09,289 - __main__ - INFO - Storing passenger_profiles in SQLite (large dataset: 1000000 rows)
2025-08-11 14:27:11,421 - __main__ - INFO - Successfully stored passenger_profiles with 1000000 rows
2025-08-11 14:27:11,894 - __main__ - INFO - Created index idx_traffic_flight_id on airport_traffic(flight_id)
2025-08-11 14:27:12,178 - __main__ - INFO - Created index idx_traffic_airport on airport_traffic(airport)
2025-08-11 14:27:12,718 - __main__ - INFO - Created index idx_traffic_timestamp on airport_traffic(timestamp)
2025-08-11 14:27:13,392 - __main__ - INFO - Created index idx_passenger_id on passenger_profiles(passenger_id)
2025-08-11 14:27:13,732 - __main__ - INFO - Created index idx_passenger_segment on passenger_profiles(passenger_segment)
2025-08-11 14:27:14,089 - __main__ - INFO - Created index idx_class_travel on passenger_profiles(class_of_travel)
2025-08-11 14:27:14,538 - __main__ - INFO - Created index idx_flight_id on flight_info(flight_id)
2025-08-11 14:27:14,914 - __main__ - INFO - Created index idx_airline on flight_info(airline)
2025-08-11 14:27:15,234 - __main__ - INFO - Created index idx_origin on flight_info(origin)
2025-08-11 14:27:15,548 - __main__ - INFO - Created index idx_destination on flight_info(destination)
2025-08-11 14:27:15,549 - __main__ - INFO - Database indexing completed
2025-08-11 14:27:15,549 - __main__ - INFO - SQLite database creation completed
2025-08-11 14:27:15,549 - __main__ - INFO - Completed create_sqlite_db in 9.09 seconds
2025-08-11 14:27:15,549 - __main__ - INFO - Data ingestion pipeline completed successfully in 19.25 seconds
2025-08-11 14:27:15,549 - __main__ - INFO - Completed run_ingestion_pipeline in 19.25 seconds
2025-08-11 14:47:56,737 - INFO - Starting ingestion pipeline...
2025-08-11 14:47:56,738 - INFO - Loading airport_traffic...
2025-08-11 14:47:58,486 - INFO - airport_traffic: 1,000,000 rows, 9 cols, 502.0MB
2025-08-11 14:47:59,619 - INFO - Loading esop_transactions...
2025-08-11 14:47:59,633 - INFO - esop_transactions: 10,000 rows, 6 cols, 3.4MB
2025-08-11 14:47:59,643 - INFO - Loading flight_info...
2025-08-11 14:48:01,262 - INFO - flight_info: 1,000,000 rows, 10 cols, 516.8MB
2025-08-11 14:48:02,721 - INFO - Loading passenger_profiles...
2025-08-11 14:48:04,801 - INFO - passenger_profiles: 1,000,000 rows, 14 cols, 547.9MB
2025-08-11 14:48:07,416 - INFO - Stored airport_traffic: 1,000,000 rows
2025-08-11 14:48:08,934 - INFO - Stored flight_info: 1,000,000 rows
2025-08-11 14:48:11,249 - INFO - Stored passenger_profiles: 1,000,000 rows
2025-08-11 14:48:15,235 - INFO - Database indexes created
2025-08-11 14:48:15,240 - INFO - create_database completed in 9.21s
2025-08-11 14:48:15,242 - INFO - run_pipeline completed in 18.50s
