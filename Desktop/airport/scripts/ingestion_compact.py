"""
Compact Data Ingestion Pipeline for Airport Analytics Project
Streamlined version focusing on core functionality with reduced verbosity.
"""

import pandas as pd
import sqlite3
from pathlib import Path
import logging
import time
from functools import wraps
import os

# Simple logging setup
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('logs/ingestion.log'), logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def timer(func):
    """Simple timing decorator"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start = time.time()
        result = func(*args, **kwargs)
        logger.info(f"{func.__name__} completed in {time.time() - start:.2f}s")
        return result
    return wrapper

class CompactIngestionPipeline:
    """Streamlined data ingestion with essential functionality"""
    
    def __init__(self, data_folder="data", db_path="airport_data.db", threshold=50000):
        self.data_folder = Path(data_folder)
        self.db_path = db_path
        self.threshold = threshold
        self.files = {
            "airport_traffic": "airport_traffic.csv",
            "esop_transactions": "esop_transactions.csv", 
            "flight_info": "flight_info.csv.csv",
            "passenger_profiles": "passenger_profiles.csv"
        }
        self.dataframes = {}
    
    def load_csv(self, file_name, dataset_name):
        """Load CSV with basic validation"""
        file_path = self.data_folder / file_name
        logger.info(f"Loading {dataset_name}...")
        
        df = pd.read_csv(file_path, comment='#', on_bad_lines='skip', low_memory=False)
        
        if df.empty:
            raise ValueError(f"Empty dataset: {file_name}")
        
        memory_mb = df.memory_usage(deep=True).sum() / 1024**2
        logger.info(f"{dataset_name}: {len(df):,} rows, {len(df.columns)} cols, {memory_mb:.1f}MB")
        
        return df
    
    def validate_data(self, df, name):
        """Quick data quality check"""
        return {
            'shape': df.shape,
            'missing': df.isnull().sum().sum(),
            'duplicates': df.duplicated().sum(),
            'memory_mb': df.memory_usage(deep=True).sum() / 1024**2
        }
    
    @timer
    def create_database(self, large_datasets):
        """Create SQLite database with indexes"""
        if not large_datasets:
            return
            
        conn = sqlite3.connect(self.db_path)
        try:
            # Store datasets
            for name, df in large_datasets.items():
                df.to_sql(name, conn, if_exists='replace', index=False, chunksize=10000)
                logger.info(f"Stored {name}: {len(df):,} rows")
            
            # Create essential indexes
            indexes = {
                "airport_traffic": ["flight_id", "airport", "timestamp"],
                "passenger_profiles": ["passenger_id", "passenger_segment"],
                "flight_info": ["flight_id", "airline", "origin", "destination"]
            }
            
            cursor = conn.cursor()
            for table, cols in indexes.items():
                if table in large_datasets:
                    # Check which columns exist
                    cursor.execute(f"PRAGMA table_info({table})")
                    existing_cols = {row[1] for row in cursor.fetchall()}
                    
                    for col in cols:
                        if col in existing_cols:
                            idx_name = f"idx_{table}_{col}"
                            cursor.execute(f"CREATE INDEX IF NOT EXISTS {idx_name} ON {table}({col})")
            
            conn.commit()
            logger.info("Database indexes created")
            
        finally:
            conn.close()
    
    @timer
    def run_pipeline(self):
        """Execute complete ingestion pipeline"""
        logger.info("Starting ingestion pipeline...")
        
        results = {'loaded': {}, 'quality': {}, 'storage': {}}
        start_time = time.time()
        
        # Load all datasets
        for name, file_name in self.files.items():
            try:
                df = self.load_csv(file_name, name)
                self.dataframes[name] = df
                
                # Quick validation
                quality = self.validate_data(df, name)
                results['quality'][name] = quality
                
                # Storage decision
                is_large = len(df) >= self.threshold
                storage = 'SQLite' if is_large else 'Memory'
                results['storage'][name] = {'strategy': storage, 'rows': len(df)}
                results['loaded'][name] = True
                
            except Exception as e:
                logger.error(f"Failed to load {name}: {e}")
                results['loaded'][name] = False
        
        # Create database for large datasets
        large_datasets = {name: df for name, df in self.dataframes.items() 
                         if len(df) >= self.threshold}
        
        if large_datasets:
            self.create_database(large_datasets)
        
        # Summary
        execution_time = time.time() - start_time
        self.print_summary(results, execution_time)
        
        return results
    
    def print_summary(self, results, execution_time):
        """Print concise summary"""
        print("\n" + "="*50)
        print("INGESTION SUMMARY")
        print("="*50)
        print(f"Execution time: {execution_time:.1f}s")
        print(f"Datasets loaded: {sum(results['loaded'].values())}/{len(self.files)}")
        
        print("\nDataset Overview:")
        for name, quality in results['quality'].items():
            if results['loaded'][name]:
                storage = results['storage'][name]['strategy']
                print(f"  • {name}: {quality['shape'][0]:,} rows, "
                      f"{quality['memory_mb']:.1f}MB ({storage})")
                if quality['missing'] > 0:
                    print(f"    ⚠️  {quality['missing']:,} missing values")
                if quality['duplicates'] > 0:
                    print(f"    ⚠️  {quality['duplicates']:,} duplicates")
        
        print("="*50)

def main():
    """Run the compact ingestion pipeline"""
    pipeline = CompactIngestionPipeline()
    results = pipeline.run_pipeline()
    return pipeline, results

if __name__ == "__main__":
    pipeline, results = main()
