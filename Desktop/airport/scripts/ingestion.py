import pandas as pd
import sqlite3
from pathlib import Path
import logging
from typing import Dict
import os
from ast import literal_eval

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

logger = setup_logging()

def load_csv_to_df(folder_path: str, file_name: str) -> pd.DataFrame:
    """Load CSV file into DataFrame with performance optimizations"""
    try:
        file_path = Path(folder_path) / file_name
        logger.info(f"Loading {file_name} from {file_path}...")
        return pd.read_csv(
            os.path.join(data_folder, filename),
            encoding="utf-8",
            sep=",",
            parse_dates=True,
            dayfirst=True,
             on_bad_lines='skip'    
        )
    except Exception as e:
        logger.error(f"Error loading {file_name}: {str(e)}")
        raise

def create_sqlite_db(dataframes: Dict[str, pd.DataFrame], db_path: str):
    """Create SQLite database with indexing (only if columns exist)"""
    conn = sqlite3.connect(db_path)
    try:
        for name, df in dataframes.items():
            df.to_sql(name, conn, if_exists='replace', index=False)
            logger.info(f"Created table {name} with {len(df)} rows")
        
        with conn:
            cursor = conn.cursor()
            index_commands = {
                "airport_traffic": ("flight_id", "idx_flight_id"),
                "passenger_profiles": ("passenger_id", "idx_passenger_id"),
                "esop_data": ("date", "idx_store_date")
            }

            for table, (col, idx_name) in index_commands.items():
                try:
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = [row[1] for row in cursor.fetchall()]
                    if col in columns:
                        cursor.execute(f"CREATE INDEX IF NOT EXISTS {idx_name} ON {table}({col})")
                        logger.info(f"Index {idx_name} created on {table}({col})")
                    else:
                        logger.warning(f"Column '{col}' not found in table '{table}', skipping index.")
                except Exception as e:
                    logger.error(f"Error creating index {idx_name} on {table}: {e}")

        logger.info("All applicable database indexes created.")
    finally:
        conn.close()

if __name__ == "__main__":
  
    data_folder = "data"
    db_path = "airport_data.db"
    
    files = {
        "passenger_profiles": "passenger_profiles.csv",
        "esop_data": "esop_data.csv",
        "flight_info": "flight_info_data.csv",
        "airport_traffic": "airport_traffic.csv"
    }

    dfs = {name: load_csv_to_df(data_folder, fname) for name, fname in files.items()}
    create_sqlite_db(dfs, db_path)
